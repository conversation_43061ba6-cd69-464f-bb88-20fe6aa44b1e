<?php
declare(strict_types=1);

namespace app\admin\controller;

use app\admin\controller\Common;
use think\facade\Db;
use app\admin\model\FrontendMenu as FrontendMenuModel;

class FrontendMenu extends Common
{
    /**
     * 头部菜单列表
     */
    public function index()
    {
        if ($this->request->isPost()) {
            $menuOrder = json_decode($this->request->post('menu_order'), true);

            if (!empty($menuOrder)) {
                try {
                    Db::startTrans();

                    foreach ($menuOrder as $item) {
                        if (isset($item['id']) && isset($item['parent_id']) && isset($item['sort'])) {
                            FrontendMenuModel::where('id', $item['id'])
                                ->update([
                                    'parent_id' => $item['parent_id'],
                                    'sort' => $item['sort']
                                ]);
                        }
                    }

                    Db::commit();
                    return json(['code' => 1, 'msg' => '排序保存成功']);
                } catch (\Exception $e) {
                    Db::rollback();
                    return json(['code' => 0, 'msg' => '排序保存失败: ' . $e->getMessage()]);
                }
            }
        }

        $menus = FrontendMenuModel::where('status', 1)
            ->where('position', 'header')
            ->order('parent_id asc, sort asc')
            ->select()
            ->toArray();

        // 构建树形结构
        $menuTree = FrontendMenuModel::buildMenuTree($menus);

        // 生成初始顺序数据
        $initialOrder = $this->generateInitialOrder($menuTree);

        return view("", [
            'menus' => $menuTree,
            'initialOrder' => json_encode($initialOrder),
            'typeList' => FrontendMenuModel::getTypeList(),
            'positionList' => FrontendMenuModel::getPositionList(),
            'menuType' => 'header',
        ]);
    }

    /**
     * 底部菜单列表
     */
    public function footer()
    {
        if ($this->request->isPost()) {
            $menuOrder = json_decode($this->request->post('menu_order'), true);

            if (!empty($menuOrder)) {
                try {
                    Db::startTrans();

                    foreach ($menuOrder as $item) {
                        if (isset($item['id']) && isset($item['parent_id']) && isset($item['sort'])) {
                            FrontendMenuModel::where('id', $item['id'])
                                ->update([
                                    'parent_id' => $item['parent_id'],
                                    'sort' => $item['sort']
                                ]);
                        }
                    }

                    Db::commit();
                    return json(['code' => 1, 'msg' => '排序保存成功']);
                } catch (\Exception $e) {
                    Db::rollback();
                    return json(['code' => 0, 'msg' => '排序保存失败: ' . $e->getMessage()]);
                }
            }
        }

        $menus = FrontendMenuModel::where('status', 1)
            ->where('position', 'footer')
            ->order('parent_id asc, sort asc')
            ->select()
            ->toArray();

        // 构建树形结构
        $menuTree = FrontendMenuModel::buildMenuTree($menus);

        // 生成初始顺序数据
        $initialOrder = $this->generateInitialOrder($menuTree);

        return view("footer", [
            'menus' => $menuTree,
            'initialOrder' => json_encode($initialOrder),
            'typeList' => FrontendMenuModel::getTypeList(),
            'positionList' => FrontendMenuModel::getPositionList(),
            'menuType' => 'footer',
        ]);
    }

    /**
     * 添加菜单
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $data = $this->request->post();

            // 验证数据
            $validate = [
                'name' => 'require',
                'type' => 'require|number',
                'position' => 'require',
            ];

            $message = [
                'name.require' => '菜单名称不能为空',
                'type.require' => '请选择菜单类型',
                'type.number' => '菜单类型格式错误',
                'position.require' => '请选择菜单位置',
            ];

            $result = $this->validate($data, $validate, $message);
            if (true !== $result) {
                return json(['code' => 0, 'msg' => $result]);
            }

            // 如果是动态菜单，验证关联ID
            if ($data['type'] != FrontendMenuModel::TYPE_STATIC && empty($data['target_id'])) {
                return json(['code' => 0, 'msg' => '请选择关联内容']);
            }

            try {
                FrontendMenuModel::create($data);
                return json(['code' => 1, 'msg' => '添加成功']);
            } catch (\Exception $e) {
                return json(['code' => 0, 'msg' => '添加失败: ' . $e->getMessage()]);
            }
        }

        // 从URL参数获取position值，默认为header
        $position = $this->request->param('position', 'header');

        return view("", [
            'typeList' => FrontendMenuModel::getTypeList(),
            'positionList' => FrontendMenuModel::getPositionList(),
            'parentMenus' => $this->getParentMenus(null, $position),
            'position' => $position,
        ]);
    }

    /**
     * 编辑菜单
     */
    public function edit()
    {
        $id = $this->request->param('id');
        if (!$id) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }

        if ($this->request->isPost()) {
            $data = $this->request->post();

            // 验证数据
            $validate = [
                'name' => 'require',
                'type' => 'require|number',
                'position' => 'require',
            ];

            $message = [
                'name.require' => '菜单名称不能为空',
                'type.require' => '请选择菜单类型',
                'type.number' => '菜单类型格式错误',
                'position.require' => '请选择菜单位置',
            ];

            $result = $this->validate($data, $validate, $message);
            if (true !== $result) {
                return json(['code' => 0, 'msg' => $result]);
            }

            // 如果是动态菜单，验证关联ID
            if ($data['type'] != FrontendMenuModel::TYPE_STATIC && empty($data['target_id'])) {
                return json(['code' => 0, 'msg' => '请选择关联内容']);
            }

            try {
                FrontendMenuModel::where('id', $id)->update($data);
                return json(['code' => 1, 'msg' => '更新成功']);
            } catch (\Exception $e) {
                return json(['code' => 0, 'msg' => '更新失败: ' . $e->getMessage()]);
            }
        }

        $menu = FrontendMenuModel::find($id);
        if (!$menu) {
            return json(['code' => 0, 'msg' => '菜单不存在']);
        }

        return view("", [
            'menu' => $menu,
            'typeList' => FrontendMenuModel::getTypeList(),
            'positionList' => FrontendMenuModel::getPositionList(),
            'parentMenus' => $this->getParentMenus($id, $menu['position']),
        ]);
    }

    /**
     * 删除菜单
     */
    public function delete()
    {
        $id = $this->request->param('id');
        if (!$id) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }

        // 检查是否有子菜单
        $childCount = FrontendMenuModel::where('parent_id', $id)->count();
        if ($childCount > 0) {
            return json(['code' => 0, 'msg' => '请先删除子菜单']);
        }

        try {
            FrontendMenuModel::destroy($id);
            return json(['code' => 1, 'msg' => '删除成功']);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '删除失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 获取动态菜单数据
     */
    public function getDynamicData()
    {
        $type = $this->request->param('type');
        if (!$type) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }

        $menuModel = new FrontendMenuModel();
        $data = $menuModel->getDynamicMenuData($type);

        return json(['code' => 1, 'data' => $data]);
    }

    /**
     * 获取父级菜单
     */
    protected function getParentMenus($excludeId = null, $position = 'header')
    {
        $where = ['status' => 1];
        if ($excludeId) {
            $where[] = ['id', '<>', $excludeId];
        }
        $where[] = ['position', '=', $position];

        return FrontendMenuModel::where($where)
            ->field('id, name, parent_id')
            ->order('sort asc, id asc')
            ->select();
    }

    /**
     * 生成初始顺序数据
     */
    protected function generateInitialOrder($menuTree)
    {
        $orderData = [];
        $order = 0;

        foreach ($menuTree as $level1) {
            $order++;
            $orderData[] = [
                'id' => $level1['id'],
                'parent_id' => 0,
                'order' => $order
            ];

            if (!empty($level1['children'])) {
                $childOrder = 0;
                foreach ($level1['children'] as $level2) {
                    $childOrder++;
                    $orderData[] = [
                        'id' => $level2['id'],
                        'parent_id' => $level1['id'],
                        'order' => $childOrder
                    ];

                    if (!empty($level2['children'])) {
                        $child2Order = 0;
                        foreach ($level2['children'] as $level3) {
                            $child2Order++;
                            $orderData[] = [
                                'id' => $level3['id'],
                                'parent_id' => $level2['id'],
                                'order' => $child2Order
                            ];
                        }
                    }
                }
            }
        }

        return $orderData;
    }
}
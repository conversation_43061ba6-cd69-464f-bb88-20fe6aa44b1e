<?php /*a:3:{s:71:"D:\phpstudy_pro\WWW\opendelclub\app\admin\view\frontend_menu\index.html";i:1754536248;s:63:"D:\phpstudy_pro\WWW\opendelclub\app\admin\view\common\head.html";i:1751613654;s:63:"D:\phpstudy_pro\WWW\opendelclub\app\admin\view\common\foot.html";i:1752808589;}*/ ?>
<!DOCTYPE html>
<html>
<head lang="en">
    <meta charset="UTF-8">
    <title>头部菜单管理</title>
    <link type="text/css" rel="stylesheet" href="/static/admin/css/index.css"/>
<link type="text/css" rel="stylesheet" href="/static/admin/css/public.css"/>

<link rel="stylesheet" href="/static/dist/ckeditor5/ckeditor5.css" crossorigin>
<link rel="stylesheet" href="/static/ckeditor5/ckeditor5.css">

    <style>
        .menu-container {
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 20px 0;
        }
        .menu-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #ddd;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .menu-header h3 {
            margin: 0;
            color: #333;
            font-size: 16px;
        }
        .menu-content {
            padding: 20px;
        }
        .manag-radio-item {
            padding: 12px 15px;
            margin: 8px 0;
            background: #fff;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            position: relative;
            transition: all 0.2s ease;
        }
        .manag-radio-item:hover {
            border-color: #007bff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .parent {
            background: #f8f9fa;
            font-size: 13px;
            border-left: 4px solid #007bff;
        }
        .child {
            background: #fff;
            margin-left: 20px;
            border-left: 3px solid #28a745;
        }
        .child2 {
            background: #fff;
            margin-left: 40px;
            border-left: 2px solid #ffc107;
        }
        .handle {
            margin-right: 10px;
            color: #666;
            font-size: 14px;
        }
        .menu-type {
            color: #666;
            font-size: 12px;
            background: #e9ecef;
            padding: 2px 6px;
            border-radius: 3px;
            margin-left: 8px;
        }
        .menu-position {
            color: #999;
            font-size: 11px;
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 3px;
            margin-left: 8px;
        }
        .menu-actions {
            float: right;
        }
        .form-actions {
            margin-top: 20px;
            text-align: center;
        }
        .btn-primary {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary:hover {
            background: #0056b3;
        }
        .add-button {
            background: #28a745;
            color: white;
            padding: 8px 16px;
            text-decoration: none;
            border-radius: 4px;
            font-size: 14px;
        }
        .add-button:hover {
            background: #218838;
        }
    </style>
</head>

<body>
    <div class="menus_r">
        <div class="maps">
            <i></i>
            <p class="current">您当前的位置：<a href="<?php echo url('Public/main'); ?>">首页</a>&nbsp;>&nbsp;头部菜单管理</p>
            <a href="<?php echo url('add', ['position' => 'header']); ?>" class="add-button">添加菜单</a>
        </div>

        <div class="form_con">
            <table class="bor_cen">
                <thead>
                    <tr class="mid_01">
                        <td class="mid_one">菜单名称</td>
                        <td class="mid_one">类型</td>
                        <td class="mid_s">操作</td>
                    </tr>
                </thead>
                <tbody>
                <?php if(is_array($menus) || $menus instanceof \think\Collection || $menus instanceof \think\Paginator): $i = 0; $__LIST__ = $menus;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                    <tr class="mid_02">
                        <td class="mid_one"><?php echo htmlentities((string) $vo['name']); ?></td>
                        <td class="mid_one">[<?php echo htmlentities((string) $typeList[$vo['type']]); ?>]</td>
                        <td class="mid_s">
                            <a href="<?php echo url('edit', ['id' => $vo['id']]); ?>" class="basic">编辑</a>
                            <a href="javascript:void(0)" onclick="deleteMenu(<?php echo htmlentities((string) $vo['id']); ?>)" class="delete-c">删除</a>
                        </td>
                    </tr>
                    <?php if(isset($vo['children']) && !empty($vo['children'])): if(is_array($vo['children']) || $vo['children'] instanceof \think\Collection || $vo['children'] instanceof \think\Paginator): $i = 0; $__LIST__ = $vo['children'];if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo2): $mod = ($i % 2 );++$i;?>
                        <tr class="mid_02">
                            <td class="mid_one" style="padding-left:2em;">└ <?php echo htmlentities((string) $vo2['name']); ?></td>
                            <td class="mid_one">[<?php echo htmlentities((string) $typeList[$vo2['type']]); ?>]</td>
                            <td class="mid_s">
                                <a href="<?php echo url('edit', ['id' => $vo2['id']]); ?>" class="basic">编辑</a>
                                <a href="javascript:void(0)" onclick="deleteMenu(<?php echo htmlentities((string) $vo2['id']); ?>)" class="delete-c">删除</a>
                            </td>
                        </tr>
                        <?php if(isset($vo2['children']) && !empty($vo2['children'])): if(is_array($vo2['children']) || $vo2['children'] instanceof \think\Collection || $vo2['children'] instanceof \think\Paginator): $i = 0; $__LIST__ = $vo2['children'];if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo3): $mod = ($i % 2 );++$i;?>
                            <tr class="mid_02">
                                <td class="mid_one" style="padding-left:4em;">&nbsp;&nbsp;└ <?php echo htmlentities((string) $vo3['name']); ?></td>
                                <td class="mid_one">[<?php echo htmlentities((string) $typeList[$vo3['type']]); ?>]</td>
                                <td class="mid_s">
                                    <a href="<?php echo url('edit', ['id' => $vo3['id']]); ?>" class="basic">编辑</a>
                                    <a href="javascript:void(0)" onclick="deleteMenu(<?php echo htmlentities((string) $vo3['id']); ?>)" class="delete-c">删除</a>
                                </td>
                            </tr>
                            <?php endforeach; endif; else: echo "" ;endif; ?>
                        <?php endif; ?>
                        <?php endforeach; endif; else: echo "" ;endif; ?>
                    <?php endif; ?>
                <?php endforeach; endif; else: echo "" ;endif; ?>
                </tbody>
            </table>
        </div>
    </div>

    <script src="/static/admin/js/jquery.min.js"></script>
    <script>
        // 删除菜单
        function deleteMenu(id) {
            if (confirm('确定要删除这个菜单吗？')) {
                $.ajax({
                    url: '<?php echo url("delete"); ?>',
                    type: 'POST',
                    data: {id: id},
                    dataType: 'json',
                    success: function(res) {
                        if (res.code == 1) {
                            alert(res.msg);
                            location.reload();
                        } else {
                            alert(res.msg);
                        }
                    },
                    error: function() {
                        alert('删除失败，请重试');
                    }
                });
            }
        }
    </script>

    <span class="by">Powered by <a target="_blank" href="http://www.ggseo.cn/ezweb/"><?php echo htmlentities((string) config('app.WebConfig_VersionName')); ?></a></span>

<script src="/static/admin/js/jquery.min.js"></script>
<script src="/static/layer/layer.js"></script>
<script src="/static/layer/laydate/laydate.js"></script>

<script src="/static/dist/ckeditor5/ckeditor5.umd.js" crossorigin></script>
<script src="/static/dist/ckeditor5/zh-cn.umd.js" crossorigin></script>
<script src="/static/ckeditor5/ckeditor5.js"></script>

<script>
    var customEditor = new CKEditorManager();
    customEditor.initAll('.tiny-editor');
</script>

<script>
    // 获取表单和提交按钮
    const $form = $('#formId');
    const $submitBtn = $('#submitBtn');
    // 初始绑定提交事件
    if ($form.length) {
        $form.on('submit', handleSubmit);
    }
    // 提交处理函数
    async function handleSubmit(event) {
        event.preventDefault(); // 阻止默认表单提交

        // 确保所有编辑器内容已同步到 textarea
        customEditor.editors.forEach((editor, index) => {
            editor.updateSourceElement();
        });

        // 解绑提交事件（避免重复提交）
        $form.off('submit', handleSubmit);
        // 禁用提交按钮（防止重复点击）
		const originalBtnText = $submitBtn.text();
        $submitBtn.prop('disabled', true).text('Submitting...');
        try {
            const formData = new FormData($form[0]);
            const response = await fetch($form.attr('action'), {
                method: 'POST',
                body: formData,
                headers: {
                    'Accept': 'application/json',
                },
            });
            const data = await response.json();
            console.log(data)
            if (data.code === 1) {
                // 提交成功
                layer.msg(data.msg, { icon: 1, time: 2000 }, () => {
                    location.reload();
                });
            } else {
                // 提交失败
                layer.msg(data.msg, { icon: 2 });
            }
        } catch (error) {
            console.error('Error:', error);
            layer.msg("提交失败，请重试", { icon: 2 });
        } finally {
            // 无论成功或失败，重新绑定事件并恢复按钮状态
            $submitBtn.prop('disabled', false).text(originalBtnText);
            $form.on('submit', handleSubmit);
        }
    }
</script>

<script>
    //弹窗
    $(document).on('click', '.layer-trigger', function() {
        const $btn = $(this);
        const contentId = $btn.data('layer');
        const title = $btn.data('title');
        const width = $btn.data('width');
        const height = $btn.data('height');

        layer.open({
            type: 1,
            title: [title, 'font-size:18px; background-color: #0f2950; color: #fff;'],
            closeBtn: 1,
            shadeClose: true,
            area: [width, height],
            content: $("#" + contentId),
            resize: false,
            move: false
        });
    });

    //弹窗"提交"和"取消"事件
    // 表单初始化函数
    function initLayerForms() {
        $('.layer-form').each(function() {
            const $form = $(this);
            const $submitBtn = $form.find('.layer-submit-btn');
            const $closeBtn = $form.find('.layer-close-btn');

            // 提交事件
            $submitBtn.on('click', async function(e) {
                e.preventDefault();
                const $btn = $(this);
                $btn.prop('disabled', true).text('提交中...');
                try {
                    const response = await $.ajax({
                        url: $form.attr('action'),
                        method: 'POST',
                        data: new FormData($form[0]),
                        processData: false,
                        contentType: false,
                        dataType: 'json'
                    });

                    if(response.code === 1) {
                        layer.msg(response.msg, {icon: 1, time: 1500}, function() {
                            location.reload();

                            // 优先使用保存的索引
                            // if(window.layerIndex !== undefined) {
                            //     layer.close(window.layerIndex);
                            // }
                            // // 其次尝试获取当前索引
                            // else if(layer.getFrameIndex && layer.getFrameIndex(window.name)) {
                            //     layer.close(layer.getFrameIndex(window.name));
                            // }
                            // // 最后全部关闭
                            // else {
                            //     layer.closeAll();
                            // }

                            // // 如果需要刷新
                            // if(response.reload) {
                            //     location.reload();
                            // }
                        });
                    } else {
                        layer.msg(response.msg, {icon: 2});
                    }
                } catch(error) {
                    layer.msg('提交失败', {icon: 2});
                } finally {
                    $btn.prop('disabled', false).text('提交');
                }
            });

            // 取消按钮
            $closeBtn.on('click', function() {
                if(window.layerIndex !== undefined) {
                    layer.close(window.layerIndex);
                } else {
                    layer.closeAll();
                }
            });
        });
    }

    // 页面加载后初始化
    $(function() {
        initLayerForms();
    });
</script>

<script>
    //无限添加按钮
    $(document).ready(function () {
        // 为已有的删除按钮绑定点击事件
        $('.class_con .list-item button').on('click', function () {
            $(this).closest('.list-item').remove(); // 删除当前列表项
        });

        // 为所有 .add-item 按钮绑定点击事件
        $('.add-item').on('click', function () {
            const containerId = $(this).data('container'); // 获取目标容器的 ID
            const titleName = $(this).data('title-name'); // 获取标题字段名称
            const urlName = $(this).data('url-name');
            const contentName = $(this).data('content-name'); // 获取内容字段名称
            const fileName = $(this).data('file-name'); // 获取文件字段名称（可选）
            const hiddenFields = $(this).data('hidden-fields');  // 隐藏字段，可以是字符串或数组

            addListItem(containerId, titleName, urlName, contentName, fileName, hiddenFields); // 调用封装好的函数
        });

        // 封装添加列表项的函数
        function addListItem(containerId, titleName, urlName, contentName, fileName, hiddenFields="") {
            const listItem = $('<div>').addClass('list-item');

            // 将隐藏字段转换为数组（支持逗号分隔的字符串或数组）
            const hiddenFieldsArray = typeof hiddenFields === 'string'
                ? hiddenFields.split(',')
                : (Array.isArray(hiddenFields) ? hiddenFields : []);

            // 标题输入框
            if (titleName) {
                const titleInput = $('<input>').attr({
                    type: 'text',
                    placeholder: '请输入标题',
                    name: titleName+"[]"
                });
                if (hiddenFieldsArray.includes(titleName)) {
                    titleInput.hide(); // 使用jQuery的hide()方法设置display:none
                }
                listItem.append(titleInput);
            }

            if (urlName) {
                const urlInput = $('<input>').attr({
                    type: 'text',
                    placeholder: '请输入网址',
                    name: urlName+"[]"
                });
                if (hiddenFieldsArray.includes(urlName)) {
                    urlInput.hide();
                }
                listItem.append(urlInput);
            }

            // 内容输入框
            if(contentName){
                const contentTextarea = $('<textarea>').attr({
                    placeholder: '请输入内容',
                    name: contentName+"[]"
                });
                if (hiddenFieldsArray.includes(contentName)) {
                    contentTextarea.hide();
                }
                listItem.append(contentTextarea);
            }

            // 如果传入了 fileName，则添加文件上传字段
            if (fileName) {
                const filePathInput = $('<input>').attr({
                    type: 'hidden',
                    name: fileName+"_path[]"
                });
                listItem.append(filePathInput);

                const fileInput = $('<input>').attr({
                    type: 'file',
                    name: fileName+"[]"
                });
                if (hiddenFieldsArray.includes(fileName)) {
                    fileInput.hide();
                }
                listItem.append(fileInput);
            }

            // 删除按钮
            const deleteButton = $('<button>').text('删除').attr('type', 'button');
            deleteButton.on('click', function () {
                listItem.remove();
            });

            // 将标题、内容和删除按钮添加到列表项中
            listItem.append(deleteButton);

            // 将列表项添加到目标容器中
            $(`#${containerId}`).append(listItem);
        }
    });
</script>

<script>
    //tab标签列表
    $('.tab-button').on('click', function () {
        // 切换 Tab 按钮状态
        $('.tab-button').removeClass('active');
        $(this).addClass('active');

        // 切换 Tab 内容
        const targetTab = $(this).data('tab');
        $('.tab-pane').removeClass('active');
        $('#' + targetTab).addClass('active');
    });
</script>

<script>
    //列表选择，全选，多选
    $(document).ready(function() {
        // 遍历每一个多选框容器
        $(".multi-select-container").each(function() {
            const $container = $(this);
            const $select = $container.find(".user-multi-select");
            const $selectAllBtn = $container.find(".select-all-btn");
            const $deselectAllBtn = $container.find(".deselect-all-btn");
            const $countDisplay = $container.find(".count");

            // 初始化计数
            updateSelectedCount();

            // 全选
            $selectAllBtn.on("click", function() {
                $select.find("option").prop("selected", true);
                updateSelectedCount();
            });

            // 取消全选
            $deselectAllBtn.on("click", function() {
                $select.find("option").prop("selected", false);
                updateSelectedCount();
            });

            // 点击选项（无需按Ctrl）
            $select.on("mousedown", "option", function(e) {
                e.preventDefault();
                $(this).prop("selected", !$(this).prop("selected"));
                $select.trigger("change"); // 触发change事件更新计数
            });

            // 选择变化时更新计数
            $select.on("change", updateSelectedCount);

            // 更新已选项数量
            function updateSelectedCount() {
                const selectedCount = $select.find("option:selected").length;
                $countDisplay.text(selectedCount);
            }
        });
    });
</script>

<script>
    //开关按钮控制
    document.querySelectorAll('.toggle-switch').forEach(switchEl => {
        switchEl.addEventListener('change', function() {
            // 获取对应的 hidden 输入字段
            const targetName = this.getAttribute('data-target');
            const hiddenInput = document.querySelector(`input[name="${targetName}"]`);

            // 更新值
            hiddenInput.value = this.checked ? '1' : '0';
        });
    });
</script>
</body>
</html>
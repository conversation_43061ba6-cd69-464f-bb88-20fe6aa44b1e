<?php /*a:6:{s:60:"D:\phpstudy_pro\WWW\opendelclub\app\home\view\error\404.html";i:1753152713;s:64:"D:\phpstudy_pro\WWW\opendelclub\app\home\view\public\header.html";i:1754531934;s:72:"D:\phpstudy_pro\WWW\opendelclub\app\home\view\public\dynamic_header.html";i:1754535300;s:64:"D:\phpstudy_pro\WWW\opendelclub\app\home\view\public\footer.html";i:1754531958;s:72:"D:\phpstudy_pro\WWW\opendelclub\app\home\view\public\dynamic_footer.html";i:1754528308;s:62:"D:\phpstudy_pro\WWW\opendelclub\app\home\view\public\foot.html";i:1753068496;}*/ ?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0,user-scalable=no">
    <title>Page not found</title>
    <meta name="robots" content="noindex">
    <link rel="shortcut icon" href="/favicon.ico" />
    <link href="/static/home/<USER>/vendors/aos.css" rel="stylesheet">
    <link href="/static/home/<USER>/style.css" rel="stylesheet" />
</head>

<body>
    <section
        class="pb-5 md:pb-20 relative bg-[url(/static/home/<USER>/bg.jpg)] ">
        <span class="absolute top-0 left-0 w-full h-full bg-[url(/static/home/<USER>/tm-left.png)] bg-no-repeat bg-[top_left] z-0 bg-size-[10rem] md:bg-size-[26.9375rem]"></span>
        <span class="absolute top-0 right-0 w-full h-full bg-[url(/static/home/<USER>/tm-right.png)] bg-no-repeat bg-[top_right] z-0 bg-size-[10rem] md:bg-size-[25.0625rem]"></span>

        <div id="header" class="relative z-[9999]">
    <header class="w-11/12 mx-auto md:w-10/12">
        <nav class="nav-wrapper flex justify-between items-center h-[3.125rem] md:h-24 top-0 relative" role="navigation">
            <div class="flex-1 md:flex md:items-center md:gap-5 md:h-full md:relative md:z-0">
                <a href="/">
                    <img src="/static/home/<USER>/logo.png" alt="HitGen OpenDEL™" class="w-24 md:w-[8.4375rem]" />
                </a>
                <ul class="fixed hidden items-center h-[calc(100%-3.125rem)] bottom-0 overflow-auto left-0 z-[99999] w-full bg-white text-base
                md:flex md:gap-x-[3.75rem] md:relative md:bg-transparent md:overflow-visible md:h-full md:flex-1 md:text-xl animate__animated animate__delay"
                    id="nav_list">
                    <?php if(isset($header_menus)): if(is_array($header_menus) || $header_menus instanceof \think\Collection || $header_menus instanceof \think\Paginator): $i = 0; $__LIST__ = $header_menus;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$menu): $mod = ($i % 2 );++$i;?>
<li class="<?php echo !empty($menu['children']) ? 'relative flex justify-between items-center border-b border-[#e0eaff] cursor-pointer navigation flex-wrap group md : border-0 md:h-full md:flex-nowrap':'md:h-full flex'; ?>">
    <?php if($menu['type'] == 1): ?>
        <!-- 静态链接 -->
        <a href="<?php echo htmlentities((string) $menu['url']); ?>" class="text-[#000] <?php echo !empty($menu['children']) ? 'cursor-pointer leading-[3.125rem] px-5 md : px-0 md:leading-none':'flex-1 leading-[3.125rem] px-5 border-b border-[#e0eaff] md:border-0 md:leading-none md:flex md:items-center'; ?>">
            <?php echo htmlentities((string) $menu['name']); ?>
        </a>
    <?php else: ?>
        <!-- 动态菜单 -->
        <a href="<?php echo htmlentities((string) (isset($menu['url']) && ($menu['url'] !== '')?$menu['url']:'#')); ?>" class="text-[#000] <?php echo !empty($menu['children']) ? 'cursor-pointer leading-[3.125rem] px-5 md : px-0 md:leading-none':'flex-1 leading-[3.125rem] px-5 border-b border-[#e0eaff] md:border-0 md:leading-none md:flex md:items-center'; ?>">
            <?php echo htmlentities((string) $menu['name']); ?>
        </a>
    <?php endif; if($menu['children']): ?>
        <i class="w-[1rem] h-[.5625rem] inline-block bg-[url(/static/home/<USER>/icons/dow.png)] bg-no-repeat bg-center bg-size-[1rem] ml-2 group-hover:rotate-180 transition-all mr-5 md:mr-0"></i>
        <ul class="secondary static z-50 hidden bg-[#f8fdff] w-full md:absolute md:top-24 md:left-1/2 md:-translate-x-1/2 md:bg-white md:w-[19.5rem]">
            <?php if(is_array($menu['children']) || $menu['children'] instanceof \think\Collection || $menu['children'] instanceof \think\Paginator): $i = 0; $__LIST__ = $menu['children'];if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$submenu): $mod = ($i % 2 );++$i;?>
            <li class="<?php echo !empty($submenu['children']) ? 'flex items-center justify-between flex-wrap border-b border-t border-[#e0eaff] relative group navigation-item' : 'grid grid-cols-1'; ?> md:pr-[1.25rem] md:px-0">
                <?php if($submenu['type'] == 1): ?>
                    <!-- 静态链接 -->
                    <a href="<?php echo htmlentities((string) $submenu['url']); ?>" class="text-[#000] flex items-center md:py-5 px-8 h-[3.125rem] md:h-auto">
                        <?php echo htmlentities((string) $submenu['name']); ?>
                    </a>
                <?php else: ?>
                    <!-- 动态菜单 -->
                    <a href="<?php echo htmlentities((string) (isset($submenu['url']) && ($submenu['url'] !== '')?$submenu['url']:'#')); ?>" class="text-[#000] flex items-center md:py-5 px-8 h-[3.125rem] md:h-auto">
                        <?php echo htmlentities((string) $submenu['name']); ?>
                    </a>
                <?php endif; if($submenu['children']): ?>
                    <i class="w-[1rem] h-[.5625rem] inline-block bg-[url(/static/home/<USER>/icons/dow.png)] bg-no-repeat rotate-0 bg-size-[1rem] md:-rotate-90 bg-center ml-2 transition-all mr-5 md:mr-0"></i>
                    <ul class="three-menu w-full hidden bg-[#f8fdff] md:absolute md:top-0 Roboto_Regular md:left-full md:w-full md:bg-[#fff] md:shadow-lg">
                        <?php if(is_array($submenu['children']) || $submenu['children'] instanceof \think\Collection || $submenu['children'] instanceof \think\Paginator): $i = 0; $__LIST__ = $submenu['children'];if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$subsubmenu): $mod = ($i % 2 );++$i;?>
                        <li class="border-b border-t border-[#e0eaff]">
                            <?php if($subsubmenu['type'] == 1): ?>
                                <a href="<?php echo htmlentities((string) $subsubmenu['url']); ?>" class="flex items-center px-8 h-[3.125rem] md:h-auto md:px-8 md:py-5 line-clamp-1">
                                    <?php echo htmlentities((string) $subsubmenu['name']); ?>
                                </a>
                            <?php else: ?>
                                <a href="<?php echo htmlentities((string) (isset($subsubmenu['url']) && ($subsubmenu['url'] !== '')?$subsubmenu['url']:'#')); ?>" class="flex items-center px-8 h-[3.125rem] md:h-auto md:px-8 md:py-5 line-clamp-1">
                                    <?php echo htmlentities((string) $subsubmenu['name']); ?>
                                </a>
                            <?php endif; ?>
                        </li>
                        <?php endforeach; endif; else: echo "" ;endif; ?>
                    </ul>
                <?php endif; ?>
            </li>
            <?php endforeach; endif; else: echo "" ;endif; ?>
            
            <!-- 动态菜单内容 -->
            <?php if($menu['type'] == 2 && isset($menu_data_$menu['id'])): if(is_array($menu_data_$menu['id']) || $menu_data_$menu['id'] instanceof \think\Collection || $menu_data_$menu['id'] instanceof \think\Paginator): $i = 0; $__LIST__ = $menu_data_$menu['id'];if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$product): $mod = ($i % 2 );++$i;?>
                <li class="<?php echo !empty($product['service']) ? 'flex items-center justify-between flex-wrap border-b border-t border-[#e0eaff] relative group navigation-item' : 'grid grid-cols-1'; ?> md:pr-[1.25rem] md:px-0">
                    <a href="/product/<?php echo htmlentities((string) $product['seo_url']); ?>" class="text-[#000] flex items-center md:py-5 px-8 h-[3.125rem] md:h-auto">
                        <?php echo htmlentities((string) $product['name']); ?>
                    </a>
                    <?php if($product['service']): ?>
                        <i class="w-[1rem] h-[.5625rem] inline-block bg-[url(/static/home/<USER>/icons/dow.png)] bg-no-repeat rotate-0 bg-size-[1rem] md:-rotate-90 bg-center ml-2 transition-all mr-5 md:mr-0"></i>
                        <ul class="three-menu w-full hidden bg-[#f8fdff] md:absolute md:top-0 Roboto_Regular md:left-full md:w-full md:bg-[#fff] md:shadow-lg">
                            <?php if(is_array($product['service']) || $product['service'] instanceof \think\Collection || $product['service'] instanceof \think\Paginator): $i = 0; $__LIST__ = $product['service'];if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$service): $mod = ($i % 2 );++$i;?>
                            <li class="border-b border-t border-[#e0eaff]">
                                <a href="/service/<?php echo htmlentities((string) $service['seo_url']); ?>" class="flex items-center px-8 h-[3.125rem] md:h-auto md:px-8 md:py-5 line-clamp-1">
                                    <?php echo htmlentities((string) $service['name']); ?>
                                </a>
                            </li>
                            <?php endforeach; endif; else: echo "" ;endif; ?>
                        </ul>
                    <?php endif; ?>
                </li>
                <?php endforeach; endif; else: echo "" ;endif; ?>
            <?php endif; if($menu['type'] == 3 && isset($menu_data_$menu['id'])): if(is_array($menu_data_$menu['id']) || $menu_data_$menu['id'] instanceof \think\Collection || $menu_data_$menu['id'] instanceof \think\Paginator): $k = 0; $__LIST__ = $menu_data_$menu['id'];if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$resource): $mod = ($k % 2 );++$k;?>
                <li class="grid grid-cols-1">
                    <a href="/resources/?tab=<?php echo htmlentities((string) $k); ?>" class="text-[#000] flex items-center md:py-5 px-8 h-[3.125rem] border-b border-[#e0eaff] md:h-auto">
                        <?php echo htmlentities((string) $resource['name']); ?>
                    </a>
                </li>
                <?php endforeach; endif; else: echo "" ;endif; ?>
            <?php endif; ?>
        </ul>
    <?php endif; ?>
</li>
<?php endforeach; endif; else: echo "" ;endif; else: ?>
                        <li class="md:h-full flex">
                            <a href="/" class="text-[#000]  flex-1 leading-[3.125rem] px-5 border-b border-[#e0eaff]
                            md:border-0 md:leading-none md:flex md:items-center
                            ">Home</a>
                        </li>
                        <li class="relative cursor-pointer md:h-full flex flex-col">
                            <a href="/news/" class="text-[#000] cursor-pointer flex-1 h-full leading-[3.125rem] px-5 flex justify-between items-center border-b border-[#e0eaff] md:border-0">
                                <span>DELHunter</span>
                            </a>
                        </li>
                        <li class="relative flex justify-between items-center border-b border-[#e0eaff] cursor-pointer navigation flex-wrap group
                            md:border-0 md:h-full md:flex-nowrap">
                            <a href="/product/"
                                class="text-[#000] cursor-pointer leading-[3.125rem] px-5 md:px-0 md:leading-none">
                                Services
                            </a>
                            <i class="w-[1rem] h-[.5625rem] inline-block bg-[url(/static/home/<USER>/icons/dow.png)] bg-no-repeat bg-center bg-size-[1rem] ml-2 group-hover:rotate-180 transition-all mr-5
                            md:mr-0
                            "></i>
                            <ul class="
                                secondary static z-50 hidden bg-[#f8fdff]
                                w-full md:absolute md:top-24 md:left-1/2  md:-translate-x-1/2 md:bg-white md:w-[19.5rem] ">
                                <?php if(is_array($menu_product) || $menu_product instanceof \think\Collection || $menu_product instanceof \think\Paginator): $i = 0; $__LIST__ = $menu_product;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                                <li class="<?php echo !empty($vo['service']) ? 'flex items-center justify-between flex-wrap border-b border-t border-[#e0eaff] relative group navigation-item' : 'grid grid-cols-1'; ?> md:pr-[1.25rem] md:px-0">
                                    <a href="/product/<?php echo htmlentities((string) $vo['seo_url']); ?>"
                                        class="text-[#000] flex items-center  md:py-5 px-8 h-[3.125rem] md:h-auto">
                                        <?php echo htmlentities((string) $vo['name']); ?>
                                    </a>
                                    <i class="w-[1rem] h-[.5625rem] inline-block bg-[url(/static/home/<USER>/icons/dow.png)] bg-no-repeat rotate-0 bg-size-[1rem] md:-rotate-90 bg-center ml-2 transition-all mr-5
                                    md:mr-0 "></i>
                                    <ul class="three-menu w-full hidden bg-[#f8fdff] md:absolute md:top-0 Roboto_Regular md:left-full md:w-full md:bg-[#fff] md:shadow-lg ">
                                        <?php if(is_array($vo['service']) || $vo['service'] instanceof \think\Collection || $vo['service'] instanceof \think\Paginator): $i = 0; $__LIST__ = $vo['service'];if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$v): $mod = ($i % 2 );++$i;?>
                                        <li class="border-b border-t border-[#e0eaff]">
                                            <a href="/service/<?php echo htmlentities((string) $v['seo_url']); ?>" class="
                                            flex items-center px-8 h-[3.125rem] md:h-auto
                                            md:px-8 md:py-5 line-clamp-1 ">
                                                <?php echo htmlentities((string) $v['name']); ?>
                                            </a>
                                        </li>
                                        <?php endforeach; endif; else: echo "" ;endif; ?>
                                    </ul>
                                </li>
                                <?php endforeach; endif; else: echo "" ;endif; ?>
                            </ul>
                        </li>

                        <li class="relative flex justify-between items-center border-b border-[#e0eaff] cursor-pointer navigation flex-wrap group
                            md:border-0 md:h-full md:flex-nowrap">
                            <a href="/resources/" class="text-[#000] cursor-pointer leading-[3.125rem] px-5 md:px-0 md:leading-none">
                                Resources
                            </a>
                            <i class="w-[1rem] h-[.5625rem] inline-block bg-[url(/static/home/<USER>/icons/dow.png)] bg-no-repeat bg-center ml-2 bg-size-[1rem] group-hover:rotate-180 transition-all mr-5
                            md:mr-0
                            "></i>
                            <ul class="
                            secondary static bg-[#f8fdff] z-50
                            hidden
                            w-full
                            md:absolute
                            md:top-24
                            md:left-1/2
                            md:-translate-x-1/2
                                md:bg-white
                                md:w-[19.5rem] ">
                                <?php if(is_array($menu_resources_category) || $menu_resources_category instanceof \think\Collection || $menu_resources_category instanceof \think\Paginator): $k = 0; $__LIST__ = $menu_resources_category;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($k % 2 );++$k;?>
                                <li class="grid grid-cols-1">
                                    <a href="/resources/?tab=<?php echo htmlentities((string) $k); ?>" class="text-[#000] flex items-center md:py-5 px-8 h-[3.125rem] border-b border-[#e0eaff] md:h-auto">
                                        <?php echo htmlentities((string) $vo['name']); ?>
                                    </a>
                                </li>
                                <?php endforeach; endif; else: echo "" ;endif; ?>

                                <li class="grid grid-cols-1">
                                    <a href="/faq/" class="text-[#000] flex items-center md:py-5 px-8 h-[3.125rem] border-b border-[#e0eaff] md:h-auto">
                                        FAQ
                                    </a>
                                </li>
                            </ul>
                        </li>

                        <?php if($basic['forum_status']==1): ?>
                        <li class="md:h-full grid grid-cols-1">
                            <a href="/iCommunity/" class="text-[#000] border-b border-[#e0eaff] leading-[3.125rem] px-5 md:border-0
                            md:leading-none
                            md:flex
                            md:items-center">iCommunity</a>
                        </li>
                        <?php endif; ?>
                    <?php endif; ?>
                </ul>
            </div>

            <div class="flex items-center gap-3.5 md:gap-8 md:relative md:z-20">
                <div class="md:flex md:items-center">
                    <div class="bg-white rounded-full p-1 h-8 w-8 border border-[#dfe7ff] md:w-12 md:h-12 "
                        id="search_btn">
                        <button type="button"
                            class="search-btn w-full h-full bg-[url(/static/home/<USER>/icons/sousuo.png)] bg-size-[1rem] rounded-full bg-no-repeat bg-center md:bg-size-[1.3rem] cursor-pointer"
                            aria-label="搜索"></button>
                    </div>
                    <form action="/search" method="get"
                        class="hidden absolute w-full left-0 top-full md:relative z-10" id="search_form">
                        <div
                            class="bg-[#f8fdff] h-14 md:bg-white md:rounded-full md:ml-2 md:w-[18.75rem] md:h-12 relative flex items-center px-2.5 md:p-0">
                            <input type="text"
                                name="q"
                                value="<?php echo isset($keyword) ? htmlentities((string) $keyword) : ''; ?>"
                                class="border flex-1 rounded-md h-10 px-5 border-[#e0eaff] bg-white md:rounded-full md:ml-2 md:w-full md:h-12 md:px-5"
                                placeholder="Enter the keyword" />
                            <button type="submit" tabindex="-1"
                                class="w-8 h-8 md:absolute md:right-[5px] md:top-1/2 md:-translate-y-1/2 md:w-[2.375rem] md:h-[2.375rem] bg-[url(/static/home/<USER>/icons/sousuo.png)] rounded-full bg-no-repeat bg-center bg-size-[1rem] md:bg-size-[1.3rem] z-10 cursor-pointer"
                                id="submit_btn"></button>
                        </div>
                    </form>
                </div>

                <?php if(session('userId')): ?>
                <div class="bg-white rounded-full p-1 h-8 w-8 border border-[#dfe7ff] relative cursor-pointer md:w-12 md:h-12 flex justify-center items-center"
                 id="message_btn" onmouseenter="showMessageDrop()" onmouseleave="hideMessageDrop()"
                    onclick="toggleMessageDrop()">
                    <div class="btn-message">
                        <div class="w-full h-full flex items-center justify-center">
                            <img src="/static/home/<USER>/icons/lingdang.png" alt="" class="w-[1rem] md:w-[1.2rem]">
                        </div>
                        <?php if($user_message_count>0): ?>
                        <div class="absolute -top-1 -right-[.525rem] text-white text-center" id="message_num">
                            <div class="min-w-[1rem] min-h-[1rem] px-1 text-[.75rem] bg-[#ff0000] rounded-full md:min-w-[1.25rem] md:h-[1.25rem] md:leading-[1.25rem]">
                                <span><?php echo htmlentities((string) $user_message_count); ?></span>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>

                    <!-- Message下拉通知 -->
                    <div class="absolute top-full left-[10%] -translate-x-1/2 z-20 min-w-[15.625rem] cursor-default pt-3
                                md:min-w-[25rem] md:-left-[100%]" id="message_drop" onclick="event.stopPropagation()" style="display: none;">
                        <?php if(count($user_message)>0): ?>
                        <div class="bg-white shadow-2xl rounded-tl-xl rounded-tr-xl">
                            <div class="message-header flex items-center justify-between p-3 border-b border-[#e0eaff] text-base md:text-xl md:py-[1.25rem] md:px-[1.875rem]">
                                <h3 class="font-bold">Tooltip</h3>
                                <button type="button"
                                    class="w-[1rem] h-[1rem] bg-[url(/static/home/<USER>/icons/shanchu.png)] bg-no-repeat bg-size-[.95rem] cursor-pointer md:w-[1.25rem] md:h-[1.375rem] md:bg-size-[1rem]"
                                    id="EmptyMessage"></button>
                            </div>
                            <div class="message-tips-list" id="message_cont">
                                <ul>
                                    <?php if(is_array($user_message) || $user_message instanceof \think\Collection || $user_message instanceof \think\Paginator): $i = 0; $__LIST__ = $user_message;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                                    <li>
                                        <a href="/user/message" class="flex items-center line-clamp-1 gap-x-1">
                                            <strong>[System]</strong><span><?php echo htmlentities((string) $vo['content']); ?></span>
                                        </a>
                                    </li>
                                    <?php endforeach; endif; else: echo "" ;endif; ?>
                                </ul>
                            </div>
                            <!-- <div class="flex items-center justify-center py-4 gap-2.5" id="message_more">
                                <a href="/user/message">
                                    <strong class="text-sm md:text-xl"> View All </strong>
                                </a>
                                <img src="/static/home/<USER>/icons/changjiantou-zuoshang2.png" alt="" class="w-[1rem]">
                            </div> -->
                            <!-- 清空/暂无消息时候显示 -->
                            <div class="text-center py-4 text-[#f08411] no-message hidden" id="no_message">
                                <span class="text-sm md:text-xl">No message</span>
                            </div>
                        </div>
                        <?php endif; if(count($user_icommunity)>0 && $basic['letter_status']==1): ?>
                        <div class="bg-white shadow-2xl rounded-bl-xl rounded-br-xl">
                            <div class="message-header flex items-center justify-between p-3 border-b border-[#e0eaff] text-base md:text-xl md:py-[1.25rem] md:px-[1.875rem]">
                                <h3 class="font-bold">iCommunity</h3>
                                <button type="button"
                                    class="w-[1rem] h-[1rem] bg-[url(/static/home/<USER>/icons/shanchu.png)] bg-no-repeat bg-size-[.95rem] cursor-pointer md:w-[1.25rem] md:h-[1.375rem] md:bg-size-[1rem]"
                                    id="EmptyMessage"></button>
                            </div>
                            <div class="message-tips-list" id="message_cont">
                                <ul>
                                    <?php if(is_array($user_icommunity) || $user_icommunity instanceof \think\Collection || $user_icommunity instanceof \think\Paginator): $i = 0; $__LIST__ = $user_icommunity;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                                    <li>
                                        <a href="/user/private-message" class="flex items-center line-clamp-1 gap-x-1">
                                            <strong>[<?php echo htmlentities((string) $vo['first_name']); ?> <?php echo htmlentities((string) $vo['last_name']); ?>]</strong>
                                            <span><?php echo htmlentities((string) $vo['content']); ?></span>
                                        </a>
                                    </li>
                                    <?php endforeach; endif; else: echo "" ;endif; ?>
                                </ul>
                            </div>
                            <!-- <div class="flex items-center justify-center py-4 gap-2.5" id="message_more">
                                <a href="/user/private-message">
                                    <strong class="text-sm md:text-xl"> View All </strong>
                                </a>
                                <img src="/static/home/<USER>/icons/changjiantou-zuoshang2.png" alt="" class="w-[1rem]">
                            </div> -->
                            <!-- 清空/暂无消息时候显示 -->
                            <div class="text-center py-4 text-[#f08411] no-message hidden" id="no_message">
                                <span class="text-sm md:text-xl">No message</span>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="w-8 h-8 rounded-full md:w-[3.125rem] md:h-[3.125rem] relative cursor-pointer btn-name">
                    <div class="user-name">
                        <img src="<?php echo isset($user['avatar']) ? htmlentities((string) $user['avatar']) : '/static/home/<USER>/user-1.jpg'; ?>" class="w-full h-full rounded-full object-cover" alt="<?php echo session('userEmail'); ?>" />
                    </div>
                    <!-- 登录以后的下拉 -->
                    <div class="user-name-drop absolute top-full -left-[50%] -translate-x-1/2 min-w-[12.5rem] pt-3 z-20 md:right-0 md:min-w-[15.625rem] md:-left-full cursor-default" onclick="event.stopPropagation()">
                        <div class="bg-white p-[1.25rem] shadow-2xl rounded-xl  md:p-6 ">
                            <div class="text-center mb-3">
                                <img src="<?php echo isset($user['avatar']) ? htmlentities((string) $user['avatar']) : '/static/home/<USER>/user-1.jpg'; ?>" alt="" class="w-[4rem] h-[4rem] mx-auto rounded-full" />
                                <p class="mt-[.625rem] text-lg">
                                    <strong class="line-clamp-1"><?php echo htmlentities((string) $user['first_name']); ?> <?php echo htmlentities((string) $user['last_name']); ?></strong>
                                </p>
                            </div>
                            <div class="text-sm md:text-xl">
                                <div class="border-b py-2  px-2 border-[#e0eaff]">
                                    <a href="/user/">
                                        Personal Center
                                    </a>
                                </div>
                                <div class="py-2 px-2">
                                    <a href="/logout">
                                        Sign Out
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php else: ?>
                <div class="flex items-center h-8 min-w-20">
                    <button aria-label="登录账户"
                        class="bg-[#f08411] h-full w-full text-white cursor-pointer bg-[url(/static/home/<USER>/icons/yonghu.png)] bg-no-repeat bg-size-[0.8125rem] bg-[0.75rem_center] rounded-lg text-sm pl-6 pr-2 md:w-32 md:h-12 md:bg-size-[1.1rem] md:text-xl">
                        <a href="/login/">Login</a>
                    </button>
                </div>
                <?php endif; ?>

                <div class="menu min-w-8 h-5 ml-2 md:hidden">
                    <button aria-label="打开菜单"
                        class="h-full w-full bg-[url(/static/home/<USER>/icons/menu.png)] bg-no-repeat bg-center bg-size-[1.3rem] cursor-pointer"
                        id="menu_btn"></button>
                </div>
        </nav>
    </header>
</div>

        <div class="w-11/12 mx-auto pt-8 md:min-h-auto md:pt-28 md:mb-20 md:w-10/12 flex flex-col items-center justify-center" style="min-height: 50vh;">
            <div class="Roboto_LightItalic flex justify-center mb-4 md:mb-12 aos-init aos-animate" data-aos="fade-right">
                <span class="bg-[rgba(102,102,102,0.1)] text-[#333] inline-block px-4 py-2 rounded-full rounded-bl-none text-sm md:w-[50rem] md:h-12 md:text-xl md:text-center" role="doc-subtitle">
                    “ Oops, something went wrong! The page you are trying to access does not exist... ”</span>
            </div>
            <div class="w-11/12 text-center mx-auto flex flex-col md:items-center aos-init aos-animate" data-aos="fade-up">
                <h1 class="text-6xl text-[#333333] md:text-8xl md:mb-10 italic">404</h1>
                <p class="text-[#666] text-xs text-center my-4 md:text-xl md:max-w-[37.5rem] md:mb-10">
                    You can continue accessing through the following methods:
                </p>
                <div class="grid grid-cols-2 justify-center items-center gap-x-2.5 flex-col text-sm md:flex-row md:text-xl">
                    <a href="/" class="flex justify-center items-center relative gap-2 text-white p-3 rounded-md bg-[#105eb3] md:h-16 md:justify-evenly md:w-[18.75rem]">
                        <span> Return to Home </span>
                        <img src="/static/home/<USER>/icons/changjiantou-zuoshang.png" alt="" class="w-3 right-7 md:w-[1.25rem] md:relative md:right-0">
                    </a>
                    <a href="javascript:void(0)" onclick="history.back()" class="flex justify-center items-center relative gap-2 text-white p-3 rounded-md bg-[#105eb3] md:h-16 md:justify-evenly md:w-[18.75rem]">
                        <span> Go Return </span>
                        <img src="/static/home/<USER>/icons/changjiantou1.png" alt="" class="w-[1rem] right-7 md:w-auto md:relative md:right-0">
                    </a>
                </div>
            </div>
        </div>
    </section>

    <section class="flex items-center justify-center py-6 md:py-10 border-t border-[#e0eaff]">
    <figure>
        <img src="/static/home/<USER>/logo.png" alt="logo" class="w-[9.375rem] md:w-[10rem]" />
        <figcaption class="sr-only">logo</figcaption>
    </figure>
</section>

<footer class="bg-[#155290]">
    <div
        class="flex flex-wrap justify-around max-w-2xs mx-auto text-white pt-8 pb-6 gap-y-3 md:max-w-max md:gap-x-10 md:text-xl">
        <?php if(isset($footer_menus)): if(is_array($footer_menus) || $footer_menus instanceof \think\Collection || $footer_menus instanceof \think\Paginator): $i = 0; $__LIST__ = $footer_menus;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$menu): $mod = ($i % 2 );++$i;?>
<a href="<?php echo htmlentities((string) (isset($menu['url']) && ($menu['url'] !== '')?$menu['url']:'#')); ?>">
    <?php echo htmlentities((string) $menu['name']); ?>
</a>
<?php endforeach; endif; else: echo "" ;endif; else: ?>
            <a href="/">
                Open DEL
            </a>
            <a href="/about">
                About
            </a>
            <a href="/contact">
                Contact Us
            </a>
            <a href="/privacy/terms">
                Terms of Service
            </a>
            <a href="/privacy/">
                Privacy Agreement
            </a>
        <?php endif; ?>
    </div>
    <div class="border-t border-[#2873bf] p-4">
        <p class="text-xs text-center text-white md:text-xl">
            <?php echo htmlentities((string) $basic['copyright']); ?> <a href="https://www.miit.gov.cn/" target="_blank"
                rel="noopener noreferrer"><?php echo htmlentities((string) $basic['record']); ?></a>
        </p>
    </div>

    <!-- 机器人 -->
    <div class="robot fixed right-5 top-1/2 z-50 -translate-y-1/2">
        <div class="robot-img relative w-14 h-14 border border-white rounded-full md:w-20 md:h-20">
            <span
                class="absolute inline-flex h-full w-full animate-ping rounded-full bg-[#105eb3] opacity-75 -z-[1]"></span>
            <a href="http://drt.zoosnet.net/LR/Chatpre.aspx?id=DRT22534948&lng=en" target="_blank">
                <img src="/static/home/<USER>/robot.png" alt="robot" class="w-full h-full object-cover" />
                <span class="absolute w-5 h-5 bg-[#f08411] text-xs rounded-full text-white flex items-center justify-center -top-2 right-0
                    md:w-6 md:h-6 md:text-base md:right-3">1</span>
            </a>
        </div>
        <div
            class="robot-text absolute bg-white right-[110%] bottom-0 min-w-[13.5rem] rounded-xl rounded-br-none p-4 text-sm md:min-w-[25.9375rem] md:p-7 md:text-xl border-[#dfe7ff] border">
            <span
                class="absolute w-5 h-5 md:w-7 md:h-7 rounded-full bg-[url(/static/home/<USER>/icons/close.png)] bg-no-repeat bg-center bg-cover -left-6 top-0 cursor-pointer md:-left-10"
                id="btn_close"></span>
            <p class="leading-6 md:leading-8">
                welcome back! still wondering if we are a good match?<img src="/static/home/<USER>/icons/515.png" alt=""
                    class="mx-1.5 w-[1.3rem] inline-block"> How can we help you today?
            </p>
        </div>
    </div>

    <!-- 置顶 -->
    <div class="fixed cursor-pointer right-5 bottom-5 w-10 h-10 bg-[#f08411] md:w-14 md:h-14 rounded-full justify-center items-center z-50 hidden"
        id="popup">
        <img src="/static/home/<USER>/icons/xiazai.png" alt="" class="w-3 md:w-[1.3rem]" />
    </div>
</footer>

<div class="modal-container fixed top-0 left-0 bottom-0 right-0 bg-[rgba(21,82,144,0.5)] hidden z-50" id="pop_container">
    <section class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white border-t-2 border-[#155290] rounded-xl overflow-hidden min-w-[90%] flex flex-col justify-center items-center py-10
    md:min-w-[43.75rem] md:min-h-[28.125rem]
    ">
        <div class="mb-6">
            <h1 class="text-2xl">
                Already have an account?
            </h1>
        </div>
        <div class="mb-7 flex flex-col gap-y-4 text-center min-w-[16.25rem]">
            <a href="/login" class="bg-[#155290] text-[#fff] text-lg py-3">
                Log In
            </a>
            <a href="/quote" onclick="window.location.href = '/quote?' + localStorage.getItem('quoteParams'); return false;" class="bg-[#e0eaff] text-[#155290] text-lg py-3">
                Not now
            </a>
        </div>
        <div class="text-base text-[#333]">
            Don t have an account? <a href="/login/register" class="underline text-[#f08411] ml-1">Sign Up</a>
        </div>
        <div class="close_btn absolute w-8 h-8 rounded-full top-2 right-3 flex items-center justify-center bg-[#155290] text-white cursor-pointer"  data-close-modal>x</div>
    </section>
</div>

    <script src="/static/home/<USER>/vendors/jquery-1.8.3.min.js"></script>
<script src="/static/home/<USER>/vendors/swiper-bundle.min.js"></script>
<script src="/static/home/<USER>/vendors/aos.js"></script>

<script src="/static/layer/layer.js"></script>
<script src="/static/home/<USER>/encapsulate.js"></script>
<script src="/static/home/<USER>/index.js"></script>

<script src="/static/home/<USER>/TabSwitch.js"></script>
<script src="/static/home/<USER>/ShowMore.js"></script>
<script src="/static/home/<USER>/MultiSelect.js"></script>

<script>
    AOS.init(); //延迟加载动画效果
</script>

<script>
    // 显示弹窗的函数
    function showQuotePopup(params) {
        // 存储参数到本地存储或全局变量
        localStorage.setItem('quoteParams', params);

        $('#pop_container').removeClass('hidden').addClass('block');
        return false; // 阻止默认行为
    }

    // 统一关闭所有弹窗
    $(document).on('click', '[data-close-modal]', function() {
        $(this).closest('.modal-container').removeClass('block').addClass('hidden');
    });
</script>

<script>
    $('#EmptyMessage').on('click', function() {
        $.ajax({
            url: '/clear-message/',
            type: 'GET',
            dataType: 'json',
            success: function(response) {
                console.log(response)
                if (response.code === 1) {
                    // 清空消息内容
                    $('#message_cont ul').empty();
                    // 隐藏查看更多按钮
                    $('#message_more').hide();
                    // 显示无消息提示（移除hidden类）
                    $('#no_message').removeClass('hidden');
                }
            },
            error: function(xhr, status, error) {
                console.error("error:", error);
            }
        });
    });
</script>


</body>

</html>
<?php
namespace app\admin\model;

use think\Model;

class FrontendMenu extends Model
{
    protected $table = 'opd_frontend_menus';
    
    // 菜单类型常量
    const TYPE_STATIC = 1;      // 静态链接
    const TYPE_PRODUCT = 2;     // 产品列表
    const TYPE_RESOURCE = 3;    // 资源分类
    const TYPE_NEWS = 4;        // 新闻分类
    const TYPE_FAQ = 5;         // FAQ分类
    
    // 位置常量
    const POSITION_HEADER = 'header';
    const POSITION_FOOTER = 'footer';
    
    /**
     * 获取菜单类型列表
     */
    public static function getTypeList()
    {
        return [
            self::TYPE_STATIC => '静态链接',
            self::TYPE_PRODUCT => '产品列表',
            self::TYPE_RESOURCE => '资源分类',
            self::TYPE_NEWS => '新闻分类',
            self::TYPE_FAQ => 'FAQ分类',
        ];
    }
    
    /**
     * 获取位置列表
     */
    public static function getPositionList()
    {
        return [
            self::POSITION_HEADER => '头部菜单',
            self::POSITION_FOOTER => '底部菜单',
        ];
    }
    
    /**
     * 构建树形菜单
     */
    public static function buildMenuTree($menus, $parentId = 0)
    {
        $tree = [];
        foreach ($menus as $menu) {
            if ($menu['parent_id'] == $parentId) {
                $children = self::buildMenuTree($menus, $menu['id']);
                if ($children) {
                    $menu['children'] = $children;
                }
                $tree[] = $menu;
            }
        }
        return $tree;
    }
    
    /**
     * 获取动态菜单数据
     */
    public function getDynamicMenuData($type, $targetId = null)
    {
        switch ($type) {
            case self::TYPE_PRODUCT:
                return $this->getProductMenuData();
            case self::TYPE_RESOURCE:
                return $this->getResourceMenuData();
            case self::TYPE_NEWS:
                return $this->getNewsMenuData();
            case self::TYPE_FAQ:
                return $this->getFaqMenuData();
            default:
                return [];
        }
    }
    
    /**
     * 获取产品菜单数据
     */
    protected function getProductMenuData()
    {
        $products = \think\facade\Db::name('product')
            ->field('id, name, seo_url')
            ->where('status', 1)
            ->order('sort asc, id asc')
            ->select();
            
        foreach ($products as &$product) {
            $product['service'] = \think\facade\Db::name('product_relation')
                ->alias('pr')
                ->field('s.id, s.name, s.seo_url')
                ->leftJoin('service s', 'pr.related_id=s.id')
                ->where(['product_id' => $product['id'], 'type' => 1])
                ->order('pr.sort asc')
                ->select();
        }
        
        return $products;
    }
    
    /**
     * 获取资源分类菜单数据
     */
    protected function getResourceMenuData()
    {
        return \think\facade\Db::name('resource_category')
            ->field('id, name')
            ->order('id asc')
            ->select();
    }
    
    /**
     * 获取新闻分类菜单数据
     */
    protected function getNewsMenuData()
    {
        return \think\facade\Db::name('news_category')
            ->field('id, name')
            ->where('status', 1)
            ->order('sort asc, id asc')
            ->select();
    }
    
    /**
     * 获取FAQ分类菜单数据
     */
    protected function getFaqMenuData()
    {
        return \think\facade\Db::name('faq_category')
            ->field('id, name')
            ->where('status', 1)
            ->order('sort asc, id asc')
            ->select();
    }
} 
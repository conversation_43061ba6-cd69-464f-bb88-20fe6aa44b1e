<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
    <title>添加前台菜单</title>
    {include file="common:head"}
</head>

<body>
    <div class="menus_r">
        <div class="maps">
            <i></i>
            <p class="current">您当前的位置：后台管理&nbsp;>&nbsp;前台菜单管理&nbsp;>&nbsp;添加菜单
                <a href="{:url('index')}" class="de_y_r">返回列表</a>
            </p>
        </div>

        <div class="clas_box">
            <form action="{:url('add')}" method="post" enctype="multipart/form-data" id="formId" autocomplete="off">
                <div class="class_con">
                    <div class="form-group">
                        <label>菜单名称：</label>
                        <input type="text" name="name" class="form-control" required>
                    </div>

                    <div class="form-group">
                        <label>菜单类型：</label>
                        <select name="type" id="menuType" class="form-control" onchange="onMenuTypeChange()">
                            <option value="">请选择菜单类型</option>
                            {volist name="typeList" id="name" key="type"}
                            <option value="{$type}">{$name}</option>
                            {/volist}
                        </select>
                    </div>

                    <div class="form-group" id="urlGroup" style="display:none;">
                        <label>链接地址：</label>
                        <input type="text" name="url" class="form-control" placeholder="请输入链接地址，如：/about">
                    </div>

                    <div class="form-group" id="targetGroup" style="display:none;">
                        <label>关联内容：</label>
                        <select name="target_id" id="targetSelect" class="form-control">
                            <option value="">请选择关联内容</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>菜单位置：</label>
                        <select name="position" class="form-control">
                            {volist name="positionList" id="name" key="position"}
                            <option value="{$position}">{$name}</option>
                            {/volist}
                        </select>
                    </div>

                    <div class="form-group">
                        <label>父级菜单：</label>
                        <select name="parent_id" class="form-control">
                            <option value="0">顶级菜单</option>
                            {volist name="parentMenus" id="menu"}
                            <option value="{$menu.id}">{$menu.name}</option>
                            {/volist}
                        </select>
                    </div>

                    <div class="form-group">
                        <label>菜单图标：</label>
                        <input type="text" name="icon" class="form-control" placeholder="请输入图标类名">
                    </div>

                    <div class="form-group">
                        <label>排序：</label>
                        <input type="number" name="sort" class="form-control" value="0">
                    </div>

                    <div class="form-group">
                        <label>状态：</label>
                        <select name="status" class="form-control">
                            <option value="1">启用</option>
                            <option value="0">禁用</option>
                        </select>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">保存</button>
                    <a href="{:url('index')}" class="btn btn-default">取消</a>
                </div>
            </form>
        </div>
    </div>

    <script src="__JS__/jquery.min.js"></script>
    <script>
        function onMenuTypeChange() {
            var type = $('#menuType').val();
            var urlGroup = $('#urlGroup');
            var targetGroup = $('#targetGroup');
            
            // 隐藏所有选项
            urlGroup.hide();
            targetGroup.hide();
            
            if (type == '1') { // 静态链接
                urlGroup.show();
            } else if (type == '2' || type == '3' || type == '4' || type == '5') { // 动态菜单
                targetGroup.show();
                loadDynamicData(type);
            }
        }
        
        function loadDynamicData(type) {
            $.ajax({
                url: '{:url("getDynamicData")}',
                type: 'POST',
                data: {type: type},
                dataType: 'json',
                success: function(res) {
                    if (res.code == 1) {
                        var select = $('#targetSelect');
                        select.empty();
                        select.append('<option value="">请选择关联内容</option>');
                        
                        $.each(res.data, function(index, item) {
                            select.append('<option value="' + item.id + '">' + item.name + '</option>');
                        });
                    } else {
                        alert(res.msg);
                    }
                },
                error: function() {
                    alert('加载数据失败，请重试');
                }
            });
        }
        
        $(document).ready(function() {
            $('#formId').submit(function(e) {
                var type = $('#menuType').val();
                var url = $('input[name="url"]').val();
                var targetId = $('select[name="target_id"]').val();
                
                if (type == '1' && !url) {
                    alert('请输入链接地址');
                    e.preventDefault();
                    return false;
                }
                
                if (type != '1' && !targetId) {
                    alert('请选择关联内容');
                    e.preventDefault();
                    return false;
                }
            });
        });
    </script>
</body>
</html> 
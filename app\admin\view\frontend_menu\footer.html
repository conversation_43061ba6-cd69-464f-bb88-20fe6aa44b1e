<!DOCTYPE html>
<html>
<head lang="en">
    <meta charset="UTF-8">
    <title>底部菜单管理</title>
    {include file="common:head"}

    <style>
        .menu-container {
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 20px 0;
        }
        .menu-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #ddd;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .menu-header h3 {
            margin: 0;
            color: #333;
            font-size: 16px;
        }
        .menu-content {
            padding: 20px;
        }
        .sortable-menu {
            min-height: 20px;
            margin: 0;
        }
        .manag-radio-item {
            padding: 12px 15px;
            margin: 8px 0;
            background: #fff;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            cursor: move;
            position: relative;
            transition: all 0.2s ease;
        }
        .manag-radio-item:hover {
            border-color: #007bff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .parent {
            background: #f8f9fa;
            font-size: 13px;
            border-left: 4px solid #007bff;
        }
        .child {
            background: #fff;
            margin-left: 20px;
            border-left: 3px solid #28a745;
        }
        .child2 {
            background: #fff;
            margin-left: 40px;
            border-left: 2px solid #ffc107;
        }
        .handle {
            cursor: move;
            margin-right: 10px;
            color: #666;
            font-size: 14px;
        }
        .ui-sortable-helper {
            opacity: 0.9;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            transform: rotate(2deg);
        }
        .ui-state-highlight {
            border: 2px dashed #007bff !important;
            background: #f8f9fa !important;
            height: 50px;
            margin: 8px 0;
            border-radius: 4px;
            opacity: 0.6;
        }
        .menu-type {
            color: #666;
            font-size: 12px;
            background: #e9ecef;
            padding: 2px 6px;
            border-radius: 3px;
            margin-left: 8px;
        }
        .menu-position {
            color: #999;
            font-size: 11px;
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 3px;
            margin-left: 8px;
        }
        .menu-actions {
            float: right;
        }
        .form-actions {
            margin-top: 20px;
            text-align: center;
        }
        .btn-primary {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary:hover {
            background: #0056b3;
        }
        .add-button {
            background: #28a745;
            color: white;
            padding: 8px 16px;
            text-decoration: none;
            border-radius: 4px;
            font-size: 14px;
        }
        .add-button:hover {
            background: #218838;
        }
    </style>
</head>

<body>
    <div class="menus_r">
        <div class="maps">
            <i></i>
            <p class="current">您当前的位置：<a href="{:url('Public/main')}">首页</a>&nbsp;>&nbsp;底部菜单管理</p>
            <a href="{:url('add', ['position' => 'footer'])}" class="add-button">添加菜单</a>
        </div>

        <div class="form_con">
            <table class="bor_cen">
                <thead>
                    <tr class="mid_01">
                        <td class="mid_one">菜单名称</td>
                        <td class="mid_one">类型</td>
                        <td class="mid_s">操作</td>
                    </tr>
                </thead>
                <tbody>
                {volist name="menus" id="vo"}
                    <tr class="mid_02">
                        <td class="mid_one">{$vo.name}</td>
                        <td class="mid_one">[{$typeList[$vo.type]}]</td>
                        <td class="mid_s">
                            <a href="{:url('edit', ['id' => $vo.id])}" class="basic">编辑</a>
                            <a href="javascript:void(0)" onclick="deleteMenu({$vo.id})" class="delete-c">删除</a>
                        </td>
                    </tr>
                    {if isset($vo.children) && !empty($vo.children)}
                        {volist name="vo.children" id="vo2"}
                        <tr class="mid_02">
                            <td class="mid_one" style="padding-left:2em;">└ {$vo2.name}</td>
                            <td class="mid_one">[{$typeList[$vo2.type]}]</td>
                            <td class="mid_s">
                                <a href="{:url('edit', ['id' => $vo2.id])}" class="basic">编辑</a>
                                <a href="javascript:void(0)" onclick="deleteMenu({$vo2.id})" class="delete-c">删除</a>
                            </td>
                        </tr>
                        {if isset($vo2.children) && !empty($vo2.children)}
                            {volist name="vo2.children" id="vo3"}
                            <tr class="mid_02">
                                <td class="mid_one" style="padding-left:4em;">&nbsp;&nbsp;└ {$vo3.name}</td>
                                <td class="mid_one">[{$typeList[$vo3.type]}]</td>
                                <td class="mid_s">
                                    <a href="{:url('edit', ['id' => $vo3.id])}" class="basic">编辑</a>
                                    <a href="javascript:void(0)" onclick="deleteMenu({$vo3.id})" class="delete-c">删除</a>
                                </td>
                            </tr>
                            {/volist}
                        {/if}
                        {/volist}
                    {/if}
                {/volist}
                </tbody>
            </table>
        </div>
    </div>

    <script src="__STATIC__/admin/js/jquery.min.js"></script>
    <script>
        // 删除菜单
        function deleteMenu(id) {
            if (confirm('确定要删除这个菜单吗？')) {
                $.ajax({
                    url: '{:url("delete")}',
                    type: 'POST',
                    data: {id: id},
                    dataType: 'json',
                    success: function(res) {
                        if (res.code == 1) {
                            alert(res.msg);
                            location.reload();
                        } else {
                            alert(res.msg);
                        }
                    },
                    error: function() {
                        alert('删除失败，请重试');
                    }
                });
            }
        }
    </script>

    {include file="common:foot"}
</body>
</html>